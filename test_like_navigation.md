# Like to Chat Navigation Implementation

## Summary of Changes

### 1. Enhanced Response Model
- Created `AcceptLikeResponse` class in `lib/models/common_model/common_model.dart`
- Added `toMessageId` field to capture the response from accept like API

### 2. Updated Repository
- Modified `LikeRepository.acceptLike()` to return `AcceptLikeResponse` instead of `CommonModel`
- This allows capturing the `to_message_id` from the API response

### 3. Enhanced Like Bloc State
- Added navigation-related fields to `LikeState`:
  - `shouldNavigateToChat`: Boolean flag to trigger navigation
  - `acceptedUser`: The user that was accepted
  - `toMessageId`: The message ID for chat navigation
- Added `ResetNavigationEvent` and handler to clear navigation state

### 4. Updated Like Event
- Modified `AcceptLikeEvent` to include the `MoveInUser` object
- This ensures we have all user data available for navigation

### 5. Enhanced Like Screen
- Updated the BlocConsumer listener to handle navigation
- When `shouldNavigateToChat` is true, creates a `SearchUserData` object and navigates to chat
- Uses `to_message_id` from API response or falls back to user ID
- Resets navigation state after navigation

## Key Features

### API Integration
- The `getMoveInData` API already returns `to_message_id` in the `MoveInUser` model
- The `acceptLike` API should return `to_message_id` in the response
- If API doesn't return `to_message_id`, falls back to user's existing `toMessageId`

### Socket Integration
- Chat screen automatically handles socket connection via `joinSocket` event
- No additional socket configuration needed for the `to_message_id`

### Navigation Flow
1. User taps "Accept" (heart icon) on a Move In profile
2. `AcceptLikeEvent` is triggered with user data
3. API call is made to accept the like
4. If successful and it's an accept action:
   - User is removed from the Move In list
   - Navigation state is set with user data and `to_message_id`
5. Like screen listener detects navigation state
6. Creates `SearchUserData` object for chat
7. Navigates to chat screen using `ChatScreen.startChatWithUser()`
8. Resets navigation state

## Testing

To test this implementation:

1. Ensure you have users in the Move In tab
2. Tap the accept (heart) button on a user
3. Verify the API call is made successfully
4. Check that navigation to chat screen occurs
5. Verify the chat screen opens with the correct user
6. Confirm the user is removed from the Move In list

## Error Handling

- If API call fails, user remains in list and no navigation occurs
- If user data is missing, navigation is skipped
- Navigation state is properly reset to prevent duplicate navigations
