ChatMessageListModel deserializeChatMessageListModel(
  Map<String, dynamic> json,
) => ChatMessageListModel.fromJson(json);

class ChatMessageListModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  ChatMessageListModel({this.count, this.next, this.previous, this.results});

  ChatMessageListModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null
        ? Results.fromJson(json['results'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<ChatMessageData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <ChatMessageData>[];
      json['data'].forEach((v) {
        data!.add(ChatMessageData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ChatMessageData {
  int? id;
  String? message;
  String? type;
  bool isRead;
  String? createdAt;
  int? sentBy;

  ChatMessageData({
    this.id,
    this.message,
    this.type,
    this.isRead = false,
    this.createdAt,
    this.sentBy,
  });

  factory ChatMessageData.fromJson(Map<String, dynamic> json) {
    return ChatMessageData(
      id: json['id'] is int
          ? json['id']
          : int.tryParse(json['id']?.toString() ?? '0'),
      message: json['message']?.toString(),
      type: json['type']?.toString(),
      isRead:
          json['is_read'] == true ||
          json['is_read'] == 1 ||
          json['is_read'] == '1',
      createdAt: json['created_at']?.toString(),
      sentBy: json['sent_by'] is int
          ? json['sent_by']
          : int.tryParse(json['sent_by']?.toString() ?? '0'),
    );
  }

  factory ChatMessageData.fromMap(Map<String, dynamic> map) {
    return ChatMessageData(
      id: map['id'],
      message: map['message'],
      type: map['type'],
      isRead: map['is_read'],
      createdAt: map['created_at'],
      sentBy: map['sent_by'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['message'] = message;
    data['type'] = type;
    data['is_read'] = isRead;
    data['created_at'] = createdAt;
    data['sent_by'] = sentBy;
    return data;
  }
}
