import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';
import 'package:room_eight/views/chat/chat_screen.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});
  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> {
  @override
  void initState() {
    super.initState();
    // Load data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LikeBloc>().add(LoadMoveOutData());
      context.read<LikeBloc>().add(LoadMoveInData());
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,

        body: BlocConsumer<LikeBloc, LikeState>(
          listener: (context, state) {
            // Handle navigation to chat screen after accepting a like
            if (state.shouldNavigateToChat && state.acceptedUser != null) {
              // Create SearchUserData object for chat screen
              final chatUser = SearchUserData(
                userId: state.toMessageId ?? state.acceptedUser!.id,
                name: state.acceptedUser!.name,
                userName: state.acceptedUser!.name, // Using name as username
                profileImage: state.acceptedUser!.profileImage,
              );

              // Navigate to chat screen
              ChatScreen.startChatWithUser(context, chatUser);

              // Reset navigation state
              context.read<LikeBloc>().add(ResetNavigationEvent());
            }
          },
          builder: (context, state) {
            if (state.isLoadData || state.isAcceptingLike) {
              return Center(child: CircularProgressIndicator());
            }
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              child: Column(
                children: [
                  buildSizedBoxH(50.h),
                  _buildTabView(context),
                  Expanded(
                    child: PageView(
                      controller: context.read<LikeBloc>().pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildMoveOutView(context),
                        _buildMoveInView(context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTabView(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: TabBar(
        onTap: (index) {
          context.read<LikeBloc>().add(TabChangedEvent(index));
          // Load data for the selected tab if not already loaded
          if (index == 0) {
            // Move Out tab
            final state = context.read<LikeBloc>().state;
            if (state.moveOutData.isEmpty && !state.isLoadingMoveOut) {
              context.read<LikeBloc>().add(LoadMoveOutData());
            }
          } else if (index == 1) {
            // Move In tab
            final state = context.read<LikeBloc>().state;
            if (state.moveInData.isEmpty && !state.isLoadingMoveIn) {
              context.read<LikeBloc>().add(LoadMoveInData());
            }
          }
        },
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.all(0.r),
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildMoveOutView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveOut) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveOutData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move Out data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveOutData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        // Show data list using the old UI design
        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveOutData());
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(bottom: 80.w),
              child: Column(
                children: [
                  buildSizedBoxH(20.h),
                  ...state.moveOutData.map(
                    (user) => Column(
                      children: [
                        buildUserProfileCard(
                          context: context,
                          imageUrl: user.profileImage ?? '',
                          name: user.name?.isNotEmpty == true
                              ? user.name!
                              : 'Unknown User',
                          department: user.about ?? 'N/A',
                          onLike: () {
                            // Handle like action for move out user
                          },
                          onDislike: () {
                            // Handle dislike action for move out user
                          },
                          onFavorite: () {
                            // Handle favorite action for move out user
                          },
                        ),
                        buildSizedBoxH(20.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMoveInView(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        // Show loading indicator if data is being loaded
        if (state.isLoadingMoveIn) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show empty state if no data
        if (state.moveInData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite, size: 64.r, color: Colors.grey),
                buildSizedBoxH(16.h),
                Text(
                  'No Move In data available',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey,
                    fontSize: 16.sp,
                  ),
                ),
                buildSizedBoxH(20.h),
                ElevatedButton(
                  onPressed: () {
                    context.read<LikeBloc>().add(LoadMoveInData());
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<LikeBloc>().add(LoadMoveInData());
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(bottom: 80.w),
              child: Column(
                children: [
                  buildSizedBoxH(20.h),
                  ...state.moveInData.map(
                    (user) => Column(
                      children: [
                        buildUserProfileCard(
                          context: context,
                          imageUrl: user.profileImage ?? '',
                          name: user.name?.isNotEmpty == true
                              ? user.name!
                              : 'Unknown User',
                          department: user.about ?? 'N/A',
                          onLike: () {
                            // Show confirmation dialog before accepting like
                            showDialog(
                              context: context,
                              builder: (BuildContext dialogContext) {
                                return CustomAlertDialog(
                                  title: 'Accept Like',
                                  subtitle:
                                      'Are you sure you want to accept this like from ${user.name ?? 'this user'}?',
                                  confirmButtonText: 'Accept',
                                  cancelButtonText: 'Cancel',
                                  isLoading: false,
                                  onConfirmButtonPressed: () {
                                    Navigator.pop(dialogContext);
                                    // Handle accept like action for move in user
                                    if (user.id != null) {
                                      context.read<LikeBloc>().add(
                                        AcceptLikeEvent(
                                          profileId: user.id!,
                                          isAccept: true,
                                          user: user,
                                        ),
                                      );
                                    }
                                  },
                                  onCancelButtonPressed: () {
                                    Navigator.pop(dialogContext);
                                  },
                                );
                              },
                            );
                          },
                          onDislike: () {
                            // Handle reject like action for move in user
                            if (user.id != null) {
                              context.read<LikeBloc>().add(
                                AcceptLikeEvent(
                                  profileId: user.id!,
                                  isAccept: false,
                                ),
                              );
                            }
                          },
                          onFavorite: () {
                            // Handle favorite action for move in user
                          },
                        ),
                        buildSizedBoxH(20.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildUserProfileCard({
    required BuildContext context,
    required String imageUrl,
    required String name,
    required String department,
    required VoidCallback onLike,
    required VoidCallback onDislike,
    required VoidCallback onFavorite,
  }) {
    // Debug print to check image URL
    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: CustomImageView(
                  imagePath: ApiEndPoint.getImageUrl + imageUrl,
                  height: 180.h,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: onFavorite,
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Colors.white70,
                    child: CustomImageView(
                      imagePath: Assets.images.svgs.icons.icLike.path,
                      margin: EdgeInsets.all(10.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
          buildSizedBoxH(12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: onDislike,
                    child: Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(100.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 5.r,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      child: CustomImageView(
                        imagePath: Assets.images.svgs.icons.icClose.path,
                        margin: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ),
                  buildSizedboxW(5.w),
                  GestureDetector(
                    onTap: onLike,
                    child: Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(100.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 5.r,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      child: CustomImageView(
                        imagePath: Assets.images.svgs.icons.icWirite.path,
                        margin: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  department,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: 14.sp,
                    color: Theme.of(context).customColors.darkGreytextcolor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
