part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  final String phone;
  // final TextEditingController phoneController;
  final FocusNode phoneFocusNode;
  final TextEditingController leasePeriodController;
  final List<String> photoPaths;
  final List<String> originalPhotoPaths;
  final List<String> removedPhotoPaths; // Track removed photos
  final List<ProfilePicture>
  originalProfilePictures; // Store original ProfilePicture objects
  final String profileImagePath;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final String gender;
  final TextEditingController ageController;
  final List<String> personalityTags;
  final List<String> customPersonalityTags;
  final GlobalKey<FormState> addPersonalDetailFormKey;
  final TextEditingController? fullNameController;
  final FocusNode? fullNameFocusNode;
  final TextEditingController? dobController;
  final TextEditingController? searchController;
  final FocusNode? dobFocusNode;
  final bool isloginLoading;
  final File? userProfile;
  final String? selectedGender;
  final String? selectedPreferredGender;
  final String? selectedPeriod;
  final String? selectedSmokingPerson;
  final String? sleectedCleanLevenl;
  final String? selectedPet;
  final String? selectedClassStand;
  final List<int> selectedHabitsAndLifestyle;
  final List<int> selectedCleanlinessLivingStyle;
  final List<int> selectedInterestsHobbies;
  final bool isPickThings;
  final int selectedOption;
  final TextEditingController contactNumberController;
  final FocusNode contactNumberFocusNode;
  final TextEditingController preferredLocationsController;
  final List<LocationModel> selectedLocations;
  final TextEditingController aboutController;
  final FocusNode aboutFocusNode;
  final bool profileLoading;
  final bool isGetUserProfileLoading;
  final List<ProfileOptionModel> habitsLifestyle;
  final List<ProfileOptionModel> livingStyle;
  final List<ProfileOptionModel> interestsHobbies;

  const ProfileState({
    required this.phone,
    required this.phoneFocusNode,
    required this.leasePeriodController,
    required this.photoPaths,
    required this.originalPhotoPaths,
    required this.removedPhotoPaths,
    required this.originalProfilePictures,
    required this.profileImagePath,
    required this.nameController,
    required this.emailController,
    required this.gender,
    required this.ageController,
    required this.personalityTags,
    required this.customPersonalityTags,
    required this.addPersonalDetailFormKey,
    this.fullNameController,
    this.fullNameFocusNode,
    this.dobController,
    this.searchController,
    this.dobFocusNode,
    this.isloginLoading = false,
    this.userProfile,
    this.selectedGender,
    this.selectedPreferredGender,
    this.selectedPeriod = '9 months',
    this.selectedSmokingPerson,
    this.sleectedCleanLevenl,
    this.selectedPet,
    this.selectedClassStand,
    this.selectedHabitsAndLifestyle = const [],
    this.selectedCleanlinessLivingStyle = const [],
    this.selectedInterestsHobbies = const [],
    this.isPickThings = false,
    this.selectedOption = 0,
    required this.contactNumberController,
    required this.contactNumberFocusNode,
    required this.preferredLocationsController,
    this.selectedLocations = const [],
    required this.aboutController,
    required this.aboutFocusNode,

    this.profileLoading = false,
    this.isGetUserProfileLoading = false,
    this.habitsLifestyle = const [],
    this.livingStyle = const [],
    this.interestsHobbies = const [],
  });

  int get totalSelectedOptions =>
      selectedHabitsAndLifestyle.length +
      selectedCleanlinessLivingStyle.length +
      selectedInterestsHobbies.length;

  factory ProfileState.initial() {
    final predefinedTags = [
      'Friendly',
      'Organized',
      'Quiet',
      'Outgoing',
      'Clean',
      'Adventurous',
    ];

    return ProfileState(
      phone: '',
      phoneFocusNode: FocusNode(),
      leasePeriodController: TextEditingController(text: ''),
      photoPaths: [],
      originalPhotoPaths: [],
      removedPhotoPaths: [],
      originalProfilePictures: [],
      profileImagePath: '',
      nameController: TextEditingController(text: ''),
      emailController: TextEditingController(text: ''),
      gender: '',
      ageController: TextEditingController(text: ''),
      personalityTags: List<String>.from(predefinedTags),
      customPersonalityTags: const [],
      addPersonalDetailFormKey: GlobalKey<FormState>(),
      fullNameController: TextEditingController(),
      fullNameFocusNode: FocusNode(),
      dobController: TextEditingController(),
      dobFocusNode: FocusNode(),
      contactNumberController: TextEditingController(),
      contactNumberFocusNode: FocusNode(),
      preferredLocationsController: TextEditingController(),
      aboutController: TextEditingController(),
      aboutFocusNode: FocusNode(),
    );
  }

  ProfileState copyWith({
    String? phone,
    TextEditingController? phoneController,
    FocusNode? phoneFocusNode,
    String? leasePeriod,
    TextEditingController? leasePeriodController,
    List<String>? photoPaths,
    List<String>? originalPhotoPaths,
    List<String>? removedPhotoPaths,
    List<ProfilePicture>? originalProfilePictures,
    String? profileImagePath,
    TextEditingController? nameController,
    TextEditingController? emailController,
    String? gender,
    TextEditingController? ageController,
    List<String>? personalityTags,
    List<String>? customPersonalityTags,
    GlobalKey<FormState>? addPersonalDetailFormKey,
    TextEditingController? fullNameController,
    FocusNode? fullNameFocusNode,
    TextEditingController? dobController,
    FocusNode? dobFocusNode,
    final TextEditingController? searchController,
    bool? isloginLoading,
    File? userProfile,
    final String? selectedGender,
    final String? selectedPreferredGender,
    final String? selectedPeriod,
    final String? selectedSmokingPerson,
    final String? sleectedCleanLevenl,
    final String? selectedPet,
    final String? selectedClassStand,
    final List<int>? selectedHabitsAndLifestyle,
    final List<int>? selectedCleanlinessLivingStyle,
    final List<int>? selectedInterestsHobbies,
    final bool? isPickThings,
    final int? selectedOption,
    TextEditingController? contactNumberController,
    FocusNode? contactNumberFocusNode,
    TextEditingController? preferredLocationsController,
    List<LocationModel>? selectedLocations,
    TextEditingController? aboutController,
    FocusNode? aboutFocusNode,

    final bool? profileLoading,
    final bool? isGetUserProfileLoading,
    final List<ProfileOptionModel>? habitsLifestyle,
    final List<ProfileOptionModel>? livingStyle,
    final List<ProfileOptionModel>? interestsHobbies,
  }) {
    return ProfileState(
      phone: phone ?? this.phone,
      phoneFocusNode: phoneFocusNode ?? this.phoneFocusNode,
      leasePeriodController:
          leasePeriodController ?? this.leasePeriodController,
      photoPaths: photoPaths ?? this.photoPaths,
      originalPhotoPaths: originalPhotoPaths ?? this.originalPhotoPaths,
      removedPhotoPaths: removedPhotoPaths ?? this.removedPhotoPaths,
      originalProfilePictures:
          originalProfilePictures ?? this.originalProfilePictures,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      nameController: nameController ?? this.nameController,
      emailController: emailController ?? this.emailController,
      gender: gender ?? this.gender,
      ageController: ageController ?? this.ageController,
      personalityTags: personalityTags ?? this.personalityTags,
      customPersonalityTags:
          customPersonalityTags ?? this.customPersonalityTags,
      addPersonalDetailFormKey:
          addPersonalDetailFormKey ?? this.addPersonalDetailFormKey,
      fullNameController: fullNameController ?? this.fullNameController,
      fullNameFocusNode: fullNameFocusNode ?? this.fullNameFocusNode,
      dobController: dobController ?? this.dobController,
      dobFocusNode: dobFocusNode ?? this.dobFocusNode,
      isloginLoading: isloginLoading ?? this.isloginLoading,
      userProfile: userProfile ?? this.userProfile,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedPreferredGender:
          selectedPreferredGender ?? this.selectedPreferredGender,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      selectedSmokingPerson:
          selectedSmokingPerson ?? this.selectedSmokingPerson,
      sleectedCleanLevenl: sleectedCleanLevenl ?? this.sleectedCleanLevenl,
      selectedPet: selectedPet ?? this.selectedPet,
      selectedClassStand: selectedClassStand ?? this.selectedClassStand,
      selectedHabitsAndLifestyle:
          selectedHabitsAndLifestyle ?? this.selectedHabitsAndLifestyle,
      selectedCleanlinessLivingStyle:
          selectedCleanlinessLivingStyle ?? this.selectedCleanlinessLivingStyle,
      selectedInterestsHobbies:
          selectedInterestsHobbies ?? this.selectedInterestsHobbies,
      isPickThings: isPickThings ?? this.isPickThings,
      searchController: searchController ?? this.searchController,
      selectedOption: selectedOption ?? this.selectedOption,
      contactNumberController:
          contactNumberController ?? this.contactNumberController,
      contactNumberFocusNode:
          contactNumberFocusNode ?? this.contactNumberFocusNode,
      preferredLocationsController:
          preferredLocationsController ?? this.preferredLocationsController,
      selectedLocations: selectedLocations ?? this.selectedLocations,
      aboutController: aboutController ?? this.aboutController,
      aboutFocusNode: aboutFocusNode ?? this.aboutFocusNode,

      profileLoading: profileLoading ?? this.profileLoading,
      isGetUserProfileLoading:
          isGetUserProfileLoading ?? this.isGetUserProfileLoading,
      habitsLifestyle: habitsLifestyle ?? this.habitsLifestyle,
      livingStyle: livingStyle ?? this.livingStyle,
      interestsHobbies: interestsHobbies ?? this.interestsHobbies,
    );
  }

  @override
  List<Object?> get props => [
    phone,
    phoneFocusNode,
    leasePeriodController,
    photoPaths,
    originalPhotoPaths,
    removedPhotoPaths,
    originalProfilePictures,
    profileImagePath,
    nameController,
    emailController,
    gender,
    ageController,
    personalityTags,
    customPersonalityTags,
    addPersonalDetailFormKey,
    fullNameController,
    fullNameFocusNode,
    dobController,
    dobFocusNode,
    searchController,
    isloginLoading,
    userProfile,
    selectedGender,
    selectedPreferredGender,
    selectedPeriod,
    selectedSmokingPerson,
    sleectedCleanLevenl,
    selectedPet,
    selectedClassStand,
    selectedHabitsAndLifestyle,
    selectedCleanlinessLivingStyle,
    selectedInterestsHobbies,
    isPickThings,
    selectedOption,
    contactNumberController,
    contactNumberFocusNode,
    preferredLocationsController,
    selectedLocations,
    aboutController,
    isGetUserProfileLoading,
    aboutFocusNode,
    habitsLifestyle,
    livingStyle,
    interestsHobbies,
  ];
}
